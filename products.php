<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in to access products
requireLogin();

// Optional: Require specific role for product management (uncomment if needed)
// requireRole('Staff'); // Staff and above can manage products

// Handle form submission for adding/editing products
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Check if this is an edit request
        if (isset($_POST['action']) && $_POST['action'] === 'edit' && isset($_POST['id'])) {
            // Handle edit product
            $product_id = intval($_POST['id']);
            $product = trim($_POST['product'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $stock = filter_var($_POST['stock'] ?? 0, FILTER_VALIDATE_INT);
            $price = filter_var($_POST['price'] ?? 0.0, FILTER_VALIDATE_FLOAT);
            $status = trim($_POST['status'] ?? '');
            $package = trim($_POST['package'] ?? '');
            $description = trim($_POST['description'] ?? '');

            // Server-side validation for edit
            $errors = [];
            if (empty($product)) $errors[] = 'Product name is required';
            if (empty($category)) $errors[] = 'Category is required';
            if ($stock === false || $stock < 0) $errors[] = 'Valid stock quantity is required';
            if ($price === false || $price < 0) $errors[] = 'Valid price is required';
            if (empty($status)) $errors[] = 'Status is required';

            // Validate status
            $valid_statuses = ['In Stock', 'Low Stock', 'Out of Stock'];
            if (!empty($status) && !in_array($status, $valid_statuses)) {
                $errors[] = 'Invalid status';
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Check for duplicate product name (excluding current product)
                $check_stmt = $pdo->prepare("SELECT id FROM products WHERE product = ? AND id != ?");
                $check_stmt->execute([$product, $product_id]);
                if ($check_stmt->rowCount() > 0) {
                    $message = 'Product name already exists!';
                    $messageType = 'error';
                } else {
                    $update_stmt = $pdo->prepare("
                        UPDATE products SET product = ?, category = ?, stock = ?, price = ?, status = ?, package = ?, description = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $result = $update_stmt->execute([$product, $category, $stock, $price, $status, $package, $description, $product_id]);

                    if ($result) {
                        $message = 'Product updated successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error updating product. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        } elseif (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['id'])) {
            // Handle delete product
            $product_id = intval($_POST['id']);

            if ($product_id <= 0) {
                $message = 'Invalid product ID.';
                $messageType = 'error';
            } else {
                // First check if product exists
                $check_stmt = $pdo->prepare("SELECT product FROM products WHERE id = ?");
                $check_stmt->execute([$product_id]);
                $product = $check_stmt->fetch();

                if (!$product) {
                    $message = 'Product not found.';
                    $messageType = 'error';
                } else {
                    // Delete the product
                    $delete_stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                    $result = $delete_stmt->execute([$product_id]);

                    if ($result) {
                        $message = 'Product "' . htmlspecialchars($product['product']) . '" deleted successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error deleting product. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        } else {
            // Handle add new product
            $product = trim($_POST['product'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $stock = filter_var($_POST['stock'] ?? 0, FILTER_VALIDATE_INT);
            $price = filter_var($_POST['price'] ?? 0.0, FILTER_VALIDATE_FLOAT);
            $status = trim($_POST['status'] ?? '');
            $package = trim($_POST['package'] ?? '');
            $description = trim($_POST['description'] ?? '');

            // Server-side validation
            $errors = [];
            if (empty($product)) $errors[] = 'Product name is required';
            if (empty($category)) $errors[] = 'Category is required';
            if ($stock === false || $stock < 0) $errors[] = 'Valid stock quantity is required';
            if ($price === false || $price < 0) $errors[] = 'Valid price is required';
            if (empty($status)) $errors[] = 'Status is required';

            // Validate status
            $valid_statuses = ['In Stock', 'Low Stock', 'Out of Stock'];
            if (!empty($status) && !in_array($status, $valid_statuses)) {
                $errors[] = 'Invalid status';
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Check for duplicate product name
                $check_stmt = $pdo->prepare("SELECT id FROM products WHERE product = ?");
                $check_stmt->execute([$product]);
                if ($check_stmt->rowCount() > 0) {
                    $message = 'Product name already exists!';
                    $messageType = 'error';
                } else {
                    // Insert new product using prepared statement
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO products (product, category, stock, price, status, package, description, created, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");

                    $result = $insert_stmt->execute([
                        $product,
                        $category,
                        $stock,
                        $price,
                        $status,
                        $package,
                        $description
                    ]);

                    if ($result) {
                        $message = 'Product added successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error adding product. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        }

    } catch (PDOException $e) {
        // Log the actual error for debugging (don't show to user)
        error_log("Database error: " . $e->getMessage());
        $message = 'A database error occurred. Please try again later.';
        $messageType = 'error';
    } catch (Exception $e) {
        // Log any other errors
        error_log("General error: " . $e->getMessage());
        $message = 'An error occurred. Please try again.';
        $messageType = 'error';
    }
}
?>

<?php include("include/header.php") ?>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include("include/sidebar.php") ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <?php include("include/top_navbar.php") ?>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Product Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Manage your products and inventory</p>
                    </div>
                    <div class="mt-4 md:mt-0 space-x-2">
                        <button onclick="openSimpleAddModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> Add Product
                        </button>
                        <!-- Debug button - remove after testing -->
                        <button onclick="testSimpleModal()" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 text-sm">
                            Test Simple Modal
                        </button>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if (!empty($message)): ?>
                <div class="mb-6">
                    <div class="<?php echo $messageType == 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold"><?php echo $messageType == 'success' ? 'Success!' : 'Error!'; ?></strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-<?php echo $messageType == 'success' ? 'green' : 'red'; ?>-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.style.display='none';">
                                <title>Close</title>
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Product Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <?php
                    // Get real product statistics from database
                    try {
                        $total_products = $pdo->query("SELECT COUNT(*) as count FROM products")->fetch()['count'] ?? 0;
                        $in_stock = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'In Stock'")->fetch()['count'] ?? 0;
                        $low_stock = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'Low Stock'")->fetch()['count'] ?? 0;
                        $out_of_stock = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'Out of Stock'")->fetch()['count'] ?? 0;
                    } catch (Exception $e) {
                        $total_products = $in_stock = $low_stock = $out_of_stock = 0;
                    }
                    ?>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Products</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($total_products); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">In Stock</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($in_stock); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Low Stock</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($low_stock); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Out of Stock</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($out_of_stock); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Categories</option>
                                <option>Beef</option>
                                <option>Chicken</option>
                                <option>Camel meat</option>
                                <option>Goat meat</option>
                                <option>Lamb</option>
                                <option>Fish</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Status</option>
                                <option>In Stock</option>
                                <option>Low Stock</option>
                                <option>Out of Stock</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Prices</option>
                                <option>Under $10</option>
                                <option>$10 - $25</option>
                                <option>$25 - $50</option>
                                <option>Over $50</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">All Products</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="exportProducts()" class="text-gray-500 hover:text-gray-700" title="Export Products">
                                <i class="fas fa-file-export"></i>
                            </button>
                            <button onclick="printProducts()" class="text-gray-500 hover:text-gray-700" title="Print Products">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php
                                try {
                                    // Fetch products from database
                                    $products_query = "SELECT id, product, category, stock, price, status, package, description, created FROM products ORDER BY created DESC LIMIT 10";
                                    $products_result = $pdo->query($products_query);
                                    $products = $products_result->fetchAll();

                                    if (count($products) > 0) {
                                        foreach ($products as $product_item):
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-md bg-indigo-100 flex items-center justify-center">
                                                    <span class="text-indigo-600 font-medium"><?php echo strtoupper(substr($product_item['product'], 0, 2)); ?></span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($product_item['product']); ?></div>
                                                <div class="text-sm text-gray-500">ID: #<?php echo htmlspecialchars($product_item['id']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($product_item['category']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($product_item['package'] ?: 'N/A'); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo number_format($product_item['stock']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">$<?php echo number_format($product_item['price'], 2); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_colors = [
                                            'In Stock' => 'bg-green-100 text-green-800',
                                            'Low Stock' => 'bg-yellow-100 text-yellow-800',
                                            'Out of Stock' => 'bg-red-100 text-red-800'
                                        ];
                                        $color_class = $status_colors[$product_item['status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $color_class; ?>">
                                            <?php echo htmlspecialchars($product_item['status']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" onclick="viewSimpleProduct(<?php echo $product_item['id']; ?>)" class="text-blue-600 hover:text-blue-900 mr-3" title="View Product">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" onclick="editProduct(<?php echo $product_item['id']; ?>)" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit Product">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" onclick="deleteProduct(<?php echo $product_item['id']; ?>)" class="text-red-600 hover:text-red-900" title="Delete Product">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php
                                        endforeach;
                                    } else {
                                ?>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-box text-4xl text-gray-300 mb-2"></i>
                                            <p class="text-lg font-medium">No products found</p>
                                            <p class="text-sm">Click "Add Product" to create your first product</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                    }
                                } catch (Exception $e) {
                                    error_log("Error fetching products: " . $e->getMessage());
                                ?>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-exclamation-triangle text-4xl text-red-300 mb-2"></i>
                                            <p class="text-lg font-medium">Error loading products</p>
                                            <p class="text-sm">Please try refreshing the page</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    <?php
                                    $displayed_count = count($products ?? []);
                                    $total_count = $total_products ?? 0;
                                    ?>
                                    Showing <span class="font-medium">1</span> to <span class="font-medium"><?php echo $displayed_count; ?></span> of <span class="font-medium"><?php echo $total_count; ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        1
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        2
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        3
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <form method="POST" action="">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                        Add New Product
                                    </h3>
                                    <button type="button" onclick="closeAddModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>

                                <div class="mt-5 space-y-6">
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <!-- Product Name -->
                                        <div class="sm:col-span-4">
                                            <label for="product" class="block text-sm font-medium text-gray-700">Product Name *</label>
                                            <div class="mt-1">
                                                <input type="text" name="product" id="product" required class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Category -->
                                        <div class="sm:col-span-2">
                                            <label for="category" class="block text-sm font-medium text-gray-700">Category *</label>
                                            <select id="category" name="category" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select category</option>
                                                <option value="Beef">Beef</option>
                                                <option value="Chicken">Chicken</option>
                                                <option value="Camel meat">Camel meat</option>
                                                <option value="Goat meat">Goat meat</option>
                                               
                                                <option value="Lamb">Lamb</option>
                                                <option value="Fish">Fish</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>

                                        <!-- Stock -->
                                        <div class="sm:col-span-2">
                                            <label for="stock" class="block text-sm font-medium text-gray-700">Stock Quantity *</label>
                                            <div class="mt-1">
                                                <input type="number" name="stock" id="stock" min="0" required class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Price -->
                                        <div class="sm:col-span-2">
                                            <label for="price" class="block text-sm font-medium text-gray-700">Price *</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" name="price" id="price" step="0.01" min="0" required class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md p-2 border" placeholder="0.00">
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="sm:col-span-2">
                                            <label for="status" class="block text-sm font-medium text-gray-700">Status *</label>
                                            <select id="status" name="status" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select status</option>
                                                <option value="In Stock">In Stock</option>
                                                <option value="Low Stock">Low Stock</option>
                                                <option value="Out of Stock">Out of Stock</option>
                                            </select>
                                        </div>

                                        <!-- Package -->
                                        <div class="sm:col-span-6">
                                            <label for="package" class="block text-sm font-medium text-gray-700">Package Details</label>
                                            <div class="mt-1">
                                                <input type="text" name="package" id="package" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border" placeholder="e.g., 1kg, 500g, per piece">
                                            </div>
                                        </div>

                                        <!-- Description -->
                                        <div class="sm:col-span-6">
                                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                            <div class="mt-1">
                                                <textarea id="description" name="description" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-gray-300 rounded-md p-2" placeholder="Product description..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Add Product
                        </button>
                        <button type="button" onclick="closeAddModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Product Modal -->
    <div id="viewProductModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="view-modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="view-modal-title">
                                    <i class="fas fa-eye text-blue-600 mr-2"></i>Product Details
                                </h3>
                                <button type="button" onclick="closeViewModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <!-- Product Details Content -->
                            <div id="productDetailsContent" class="space-y-6">
                                <!-- Loading state -->
                                <div id="loadingState" class="text-center py-8">
                                    <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-500">Loading product details...</p>
                                </div>

                                <!-- Product details will be loaded here -->
                                <div id="productDetails" class="hidden">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Left Column -->
                                        <div class="space-y-4">
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <div class="flex items-center mb-3">
                                                    <div id="productAvatar" class="h-16 w-16 rounded-lg bg-indigo-100 flex items-center justify-center mr-4">
                                                        <span id="productInitials" class="text-indigo-600 font-bold text-xl"></span>
                                                    </div>
                                                    <div>
                                                        <h4 id="productName" class="text-xl font-semibold text-gray-900"></h4>
                                                        <p id="productId" class="text-sm text-gray-500"></p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="space-y-3">
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Category:</span>
                                                    <span id="productCategory" class="text-sm text-gray-900 font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Stock Quantity:</span>
                                                    <span id="productStock" class="text-sm text-gray-900 font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Price:</span>
                                                    <span id="productPrice" class="text-sm text-gray-900 font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Status:</span>
                                                    <span id="productStatus" class="text-sm font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Package:</span>
                                                    <span id="productPackage" class="text-sm text-gray-900"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="space-y-4">
                                            <div>
                                                <h5 class="text-sm font-medium text-gray-500 mb-2">Description:</h5>
                                                <div id="productDescription" class="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 min-h-[100px]"></div>
                                            </div>

                                            <div class="space-y-3">
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Created:</span>
                                                    <span id="productCreated" class="text-sm text-gray-900"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Last Updated:</span>
                                                    <span id="productUpdated" class="text-sm text-gray-900"></span>
                                                </div>
                                            </div>

                                            <!-- Stock Status Indicator -->
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <h5 class="text-sm font-medium text-gray-500 mb-2">Stock Status:</h5>
                                                <div id="stockIndicator" class="flex items-center">
                                                    <div id="stockBar" class="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                                                        <div id="stockProgress" class="h-2 rounded-full transition-all duration-300"></div>
                                                    </div>
                                                    <span id="stockPercentage" class="text-xs font-medium"></span>
                                                </div>
                                                <p id="stockMessage" class="text-xs text-gray-500 mt-1"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Error state -->
                                <div id="errorState" class="hidden text-center py-8">
                                    <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i>
                                    <p class="text-red-500">Error loading product details. Please try again.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="editProductFromView()" id="editFromViewBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </button>
                    <button type="button" onclick="printProductDetails()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-print mr-2"></i>Print
                    </button>
                    <button type="button" onclick="closeViewModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Modal for Add Product -->
    <div id="simpleAddModal" class="modal-simple">
        <div class="modal-content-simple">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Add New Product</h3>
                    <button type="button" onclick="closeSimpleAddModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" action="">
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <!-- Product Name -->
                        <div class="sm:col-span-2">
                            <label for="simple_product" class="block text-sm font-medium text-gray-700">Product Name *</label>
                            <input type="text" name="product" id="simple_product" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>

                        <!-- Category -->
                        <div>
                            <label for="simple_category" class="block text-sm font-medium text-gray-700">Category *</label>
                            <select id="simple_category" name="category" required
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                                <option value="">Select category</option>
                                <option value="Beef">Beef</option>
                                <option value="Chicken">Chicken</option>
                                <option value="Camel meat">Camel meat</option>
                                <option value="Goat meat">Goat meat</option>
                              
                                <option value="Lamb">Lamb</option>
                                <option value="Fish">Fish</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <!-- Stock -->
                        <div>
                            <label for="simple_stock" class="block text-sm font-medium text-gray-700">Stock Quantity *</label>
                            <input type="number" name="stock" id="simple_stock" min="0" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                        </div>

                        <!-- Price -->
                        <div>
                            <label for="simple_price" class="block text-sm font-medium text-gray-700">Price *</label>
                            <input type="number" name="price" id="simple_price" step="0.01" min="0" required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border" placeholder="0.00">
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="simple_status" class="block text-sm font-medium text-gray-700">Status *</label>
                            <select id="simple_status" name="status" required
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">
                                <option value="">Select status</option>
                                <option value="In Stock">In Stock</option>
                                <option value="Low Stock">Low Stock</option>
                                <option value="Out of Stock">Out of Stock</option>
                            </select>
                        </div>

                        <!-- Package -->
                        <div class="sm:col-span-2">
                            <label for="simple_package" class="block text-sm font-medium text-gray-700">Package Details</label>
                            <input type="text" name="package" id="simple_package"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border"
                                   placeholder="e.g., 1kg, 500g, per piece">
                        </div>

                        <!-- Description -->
                        <div class="sm:col-span-2">
                            <label for="simple_description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea id="simple_description" name="description" rows="3"
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border"
                                      placeholder="Product description..."></textarea>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeSimpleAddModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            Add Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Simple Modal for View Product -->
    <div id="simpleViewModal" class="modal-simple">
        <div class="modal-content-simple">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-eye text-blue-600 mr-2"></i>Product Details
                    </h3>
                    <button type="button" onclick="closeSimpleViewModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div id="simpleViewContent">
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500">Loading product details...</p>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeSimpleViewModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Modal for Edit Product -->
    <div id="simpleEditModal" class="modal-simple">
        <div class="modal-content-simple">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-edit text-indigo-600 mr-2"></i>Edit Product
                    </h3>
                    <button type="button" onclick="closeSimpleEditModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div id="simpleEditContent">
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500">Loading product data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Modal for Delete Product -->
    <div id="simpleDeleteModal" class="modal-simple">
        <div class="modal-content-simple">
            <div class="p-6 max-w-md mx-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-trash text-red-600 mr-2"></i>Delete Product
                    </h3>
                    <button type="button" onclick="closeSimpleDeleteModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div id="simpleDeleteContent">
                    <div class="text-center py-4">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Are you sure?</h3>
                        <p class="text-sm text-gray-500 mb-4">
                            This action cannot be undone. This will permanently delete the product
                            <span id="deleteProductName" class="font-medium text-gray-900"></span>
                            and remove all associated data.
                        </p>

                        <div class="flex justify-center space-x-3">
                            <button type="button" onclick="closeSimpleDeleteModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="button" onclick="confirmDeleteProduct()"
                                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                                <i class="fas fa-trash mr-2"></i>Delete Product
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include("include/footer.php") ?>

    <style>
        /* Simple modal styles that override everything */
        .modal-simple {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            z-index: 9999 !important;
            display: none !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .modal-simple.show {
            display: flex !important;
        }

        .modal-content-simple {
            background: white !important;
            border-radius: 8px !important;
            max-width: 90vw !important;
            max-height: 90vh !important;
            overflow-y: auto !important;
            position: relative !important;
            z-index: 10000 !important;
            margin: 20px !important;
        }

        /* Override Alpine.js and other conflicts */
        .modal-simple * {
            pointer-events: auto !important;
        }

        /* Ensure buttons work */
        button {
            cursor: pointer !important;
            pointer-events: auto !important;
        }

        /* Hide original modals */
        #addProductModal, #viewProductModal {
            display: none !important;
        }
    </style>

    <script>
        // Clean, Simple Modal Functions - No conflicts
        function openSimpleAddModal() {
            console.log('Opening simple add modal...');
            var modal = document.getElementById('simpleAddModal');
            if (modal) {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';

                // Focus on first input
                setTimeout(function() {
                    var firstInput = document.getElementById('simple_product');
                    if (firstInput) firstInput.focus();
                }, 100);

                console.log('Simple add modal opened');
            } else {
                alert('Modal not found. Please refresh the page.');
            }
        }

        function closeSimpleAddModal() {
            console.log('Closing simple add modal...');
            var modal = document.getElementById('simpleAddModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = '';

                // Reset form
                var form = modal.querySelector('form');
                if (form) form.reset();

                console.log('Simple add modal closed');
            }
        }

        function openSimpleViewModal() {
            console.log('Opening simple view modal...');
            var modal = document.getElementById('simpleViewModal');
            if (modal) {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('Simple view modal opened');
            }
        }

        function closeSimpleViewModal() {
            console.log('Closing simple view modal...');
            var modal = document.getElementById('simpleViewModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = '';
                console.log('Simple view modal closed');
            }
        }

        function openSimpleEditModal() {
            console.log('Opening simple edit modal...');
            var modal = document.getElementById('simpleEditModal');
            if (modal) {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('Simple edit modal opened');
            }
        }

        function closeSimpleEditModal() {
            console.log('Closing simple edit modal...');
            var modal = document.getElementById('simpleEditModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = '';
                console.log('Simple edit modal closed');
            }
        }

        function openSimpleDeleteModal() {
            console.log('Opening simple delete modal...');
            var modal = document.getElementById('simpleDeleteModal');
            if (modal) {
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
                console.log('Simple delete modal opened');
            }
        }

        function closeSimpleDeleteModal() {
            console.log('Closing simple delete modal...');
            var modal = document.getElementById('simpleDeleteModal');
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = '';
                console.log('Simple delete modal closed');
            }
        }

        // Simple view product function
        function viewSimpleProduct(id) {
            console.log('Viewing product ID:', id);
            openSimpleViewModal();

            // Load product details
            var content = document.getElementById('simpleViewContent');
            content.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i><p class="text-gray-500">Loading product details...</p></div>';

            // Fetch product details
            fetch('get_product_details.php?id=' + id)
                .then(function(response) {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(function(data) {
                    console.log('Response data:', data);
                    if (data.success && data.product) {
                        displaySimpleProductDetails(data.product);
                    } else {
                        var errorMsg = data.error || 'Unknown error occurred';
                        console.error('API Error:', errorMsg);
                        content.innerHTML = '<div class="text-center py-8"><i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i><p class="text-red-500">Error: ' + errorMsg + '</p></div>';
                    }
                })
                .catch(function(error) {
                    console.error('Fetch Error:', error);
                    content.innerHTML = '<div class="text-center py-8"><i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i><p class="text-red-500">Network error: ' + error.message + '</p></div>';
                });
        }

        function displaySimpleProductDetails(product) {
            var content = document.getElementById('simpleViewContent');
            content.innerHTML =
                '<div class="grid grid-cols-1 md:grid-cols-2 gap-6">' +
                    '<div>' +
                        '<div class="bg-gray-50 p-4 rounded-lg mb-4">' +
                            '<div class="flex items-center">' +
                                '<div class="h-12 w-12 rounded-lg bg-indigo-100 flex items-center justify-center mr-4">' +
                                    '<span class="text-indigo-600 font-bold">' + product.initials + '</span>' +
                                '</div>' +
                                '<div>' +
                                    '<h4 class="text-lg font-semibold">' + product.product + '</h4>' +
                                    '<p class="text-sm text-gray-500">ID: #' + product.id + '</p>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                        '<div class="space-y-3">' +
                            '<div class="flex justify-between"><span class="font-medium">Category:</span><span>' + product.category + '</span></div>' +
                            '<div class="flex justify-between"><span class="font-medium">Stock:</span><span>' + product.formatted_stock + '</span></div>' +
                            '<div class="flex justify-between"><span class="font-medium">Price:</span><span>' + product.formatted_price + '</span></div>' +
                            '<div class="flex justify-between"><span class="font-medium">Status:</span><span class="px-2 py-1 rounded-full text-xs ' + product.status_color + '">' + product.status + '</span></div>' +
                            '<div class="flex justify-between"><span class="font-medium">Package:</span><span>' + product.package + '</span></div>' +
                        '</div>' +
                    '</div>' +
                    '<div>' +
                        '<div class="mb-4">' +
                            '<h5 class="font-medium mb-2">Description:</h5>' +
                            '<div class="bg-gray-50 p-3 rounded text-sm">' + product.description + '</div>' +
                        '</div>' +
                        '<div class="space-y-2 text-sm">' +
                            '<div class="flex justify-between"><span class="font-medium">Created:</span><span>' + product.formatted_created + '</span></div>' +
                            '<div class="flex justify-between"><span class="font-medium">Updated:</span><span>' + product.formatted_updated + '</span></div>' +
                        '</div>' +
                    '</div>' +
                '</div>';
        }

        // Edit product function
        function editProduct(id) {
            console.log('Editing product ID:', id);
            openSimpleEditModal();

            // Load product data for editing
            var content = document.getElementById('simpleEditContent');
            content.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i><p class="text-gray-500">Loading product data...</p></div>';

            // Fetch product details
            fetch('get_product_details.php?id=' + id)
                .then(function(response) {
                    console.log('Edit response status:', response.status);
                    return response.json();
                })
                .then(function(data) {
                    console.log('Edit response data:', data);
                    if (data.success && data.product) {
                        displayEditForm(data.product);
                    } else {
                        var errorMsg = data.error || 'Unknown error occurred';
                        console.error('Edit API Error:', errorMsg);
                        content.innerHTML = '<div class="text-center py-8"><i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i><p class="text-red-500">Error: ' + errorMsg + '</p></div>';
                    }
                })
                .catch(function(error) {
                    console.error('Edit Fetch Error:', error);
                    content.innerHTML = '<div class="text-center py-8"><i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i><p class="text-red-500">Network error: ' + error.message + '</p></div>';
                });
        }

        function displayEditForm(product) {
            var content = document.getElementById('simpleEditContent');
            content.innerHTML =
                '<form method="POST" action="" onsubmit="return submitEditForm(event, ' + product.id + ')">' +
                    '<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">' +
                        '<!-- Product Name -->' +
                        '<div class="sm:col-span-2">' +
                            '<label for="edit_product" class="block text-sm font-medium text-gray-700">Product Name *</label>' +
                            '<input type="text" name="product" id="edit_product" required value="' + product.product + '" ' +
                                   'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">' +
                        '</div>' +
                        '<!-- Category -->' +
                        '<div>' +
                            '<label for="edit_category" class="block text-sm font-medium text-gray-700">Category *</label>' +
                            '<select id="edit_category" name="category" required ' +
                                    'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">' +
                                '<option value="">Select category</option>' +
                                '<option value="Beef"' + (product.category === 'Beef' ? ' selected' : '') + '>Beef</option>' +
                                '<option value="Chicken"' + (product.category === 'Chicken' ? ' selected' : '') + '>Chicken</option>' +
                                '<option value=""' + (product.category === 'Pork' ? ' selected' : '') + '>Pork</option>' +
                                '<option value="Lamb"' + (product.category === 'Lamb' ? ' selected' : '') + '>Lamb</option>' +
                                '<option value="Fish"' + (product.category === 'Fish' ? ' selected' : '') + '>Fish</option>' +
                                '<option value="Other"' + (product.category === 'Other' ? ' selected' : '') + '>Other</option>' +
                            '</select>' +
                        '</div>' +
                        '<!-- Stock -->' +
                        '<div>' +
                            '<label for="edit_stock" class="block text-sm font-medium text-gray-700">Stock Quantity *</label>' +
                            '<input type="number" name="stock" id="edit_stock" min="0" required value="' + product.stock + '" ' +
                                   'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">' +
                        '</div>' +
                        '<!-- Price -->' +
                        '<div>' +
                            '<label for="edit_price" class="block text-sm font-medium text-gray-700">Price *</label>' +
                            '<input type="number" name="price" id="edit_price" step="0.01" min="0" required value="' + product.price + '" ' +
                                   'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border" placeholder="0.00">' +
                        '</div>' +
                        '<!-- Status -->' +
                        '<div>' +
                            '<label for="edit_status" class="block text-sm font-medium text-gray-700">Status *</label>' +
                            '<select id="edit_status" name="status" required ' +
                                    'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border">' +
                                '<option value="">Select status</option>' +
                                '<option value="In Stock"' + (product.status === 'In Stock' ? ' selected' : '') + '>In Stock</option>' +
                                '<option value="Low Stock"' + (product.status === 'Low Stock' ? ' selected' : '') + '>Low Stock</option>' +
                                '<option value="Out of Stock"' + (product.status === 'Out of Stock' ? ' selected' : '') + '>Out of Stock</option>' +
                            '</select>' +
                        '</div>' +
                        '<!-- Package -->' +
                        '<div class="sm:col-span-2">' +
                            '<label for="edit_package" class="block text-sm font-medium text-gray-700">Package Details</label>' +
                            '<input type="text" name="package" id="edit_package" value="' + (product.package || '') + '" ' +
                                   'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border" ' +
                                   'placeholder="e.g., 1kg, 500g, per piece">' +
                        '</div>' +
                        '<!-- Description -->' +
                        '<div class="sm:col-span-2">' +
                            '<label for="edit_description" class="block text-sm font-medium text-gray-700">Description</label>' +
                            '<textarea id="edit_description" name="description" rows="3" ' +
                                      'class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 p-2 border" ' +
                                      'placeholder="Product description...">' + (product.description || '') + '</textarea>' +
                        '</div>' +
                    '</div>' +
                    '<div class="mt-6 flex justify-end space-x-3">' +
                        '<button type="button" onclick="closeSimpleEditModal()" ' +
                                'class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">' +
                            'Cancel' +
                        '</button>' +
                        '<button type="submit" ' +
                                'class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">' +
                            'Update Product' +
                        '</button>' +
                    '</div>' +
                    '<input type="hidden" name="action" value="edit">' +
                    '<input type="hidden" name="id" value="' + product.id + '">' +
                '</form>';
        }

        function submitEditForm(event, productId) {
            event.preventDefault();

            // Get form data
            var form = event.target;
            var formData = new FormData(form);

            // Show loading state
            var content = document.getElementById('simpleEditContent');
            content.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i><p class="text-gray-500">Updating product...</p></div>';

            // Submit form via AJAX
            fetch('products.php', {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                return response.text();
            })
            .then(function(data) {
                // Close modal and reload page to show updated data
                closeSimpleEditModal();
                alert('Product updated successfully!');
                location.reload();
            })
            .catch(function(error) {
                console.error('Update Error:', error);
                alert('Error updating product. Please try again.');
                // Reload the edit form
                editProduct(productId);
            });

            return false;
        }

        // Global variable to store product ID for deletion
        var productToDelete = null;

        function deleteProduct(id) {
            console.log('Deleting product ID:', id);
            productToDelete = id;

            // First, get product details to show product name in confirmation
            fetch('get_product_details.php?id=' + id)
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success && data.product) {
                        // Set product name in the delete modal
                        document.getElementById('deleteProductName').textContent = '"' + data.product.product + '"';
                        openSimpleDeleteModal();
                    } else {
                        // Fallback if we can't get product details
                        document.getElementById('deleteProductName').textContent = '';
                        openSimpleDeleteModal();
                    }
                })
                .catch(function(error) {
                    console.error('Error fetching product for delete:', error);
                    // Still show delete modal even if we can't get product name
                    document.getElementById('deleteProductName').textContent = '';
                    openSimpleDeleteModal();
                });
        }

        function confirmDeleteProduct() {
            if (!productToDelete) {
                alert('Error: No product selected for deletion.');
                return;
            }

            console.log('Confirming delete for product ID:', productToDelete);

            // Show loading state
            var content = document.getElementById('simpleDeleteContent');
            content.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i><p class="text-gray-500">Deleting product...</p></div>';

            // Create form data for deletion
            var formData = new FormData();
            formData.append('action', 'delete');
            formData.append('id', productToDelete);

            // Submit delete request
            fetch('products.php', {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                return response.text();
            })
            .then(function(data) {
                // Close modal and reload page to show updated data
                closeSimpleDeleteModal();
                alert('Product deleted successfully!');
                location.reload();
            })
            .catch(function(error) {
                console.error('Delete Error:', error);
                alert('Error deleting product. Please try again.');
                closeSimpleDeleteModal();
            });
        }

        // Test function for simple modals
        function testSimpleModal() {
            console.log('Testing simple modal...');
            alert('Testing simple modal - this should work!');

            var simpleAddModal = document.getElementById('simpleAddModal');
            var simpleViewModal = document.getElementById('simpleViewModal');

            console.log('Simple Add Modal exists:', !!simpleAddModal);
            console.log('Simple View Modal exists:', !!simpleViewModal);

            if (simpleAddModal) {
                console.log('Opening simple add modal...');
                openSimpleAddModal();
            } else {
                alert('Simple modal not found!');
            }
        }

        // Simple event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple modal system loaded');

            // Test if functions are defined
            console.log('openSimpleAddModal defined:', typeof openSimpleAddModal === 'function');
            console.log('viewSimpleProduct defined:', typeof viewSimpleProduct === 'function');
            console.log('testSimpleModal defined:', typeof testSimpleModal === 'function');

            // Add click outside to close functionality
            var addModal = document.getElementById('simpleAddModal');
            var viewModal = document.getElementById('simpleViewModal');
            var editModal = document.getElementById('simpleEditModal');
            var deleteModal = document.getElementById('simpleDeleteModal');

            if (addModal) {
                addModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeSimpleAddModal();
                    }
                });
            }

            if (viewModal) {
                viewModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeSimpleViewModal();
                    }
                });
            }

            if (editModal) {
                editModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeSimpleEditModal();
                    }
                });
            }

            if (deleteModal) {
                deleteModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeSimpleDeleteModal();
                    }
                });
            }

            // Simple form validation
            var simpleForm = document.querySelector('#simpleAddModal form');
            if (simpleForm) {
                simpleForm.addEventListener('submit', function(e) {
                    var product = document.getElementById('simple_product').value.trim();
                    var category = document.getElementById('simple_category').value;
                    var stock = document.getElementById('simple_stock').value;
                    var price = document.getElementById('simple_price').value;
                    var status = document.getElementById('simple_status').value;

                    if (!product || !category || !stock || !price || !status) {
                        alert('Please fill in all required fields (*).');
                        e.preventDefault();
                        return false;
                    }

                    if (parseFloat(price) < 0) {
                        alert('Price cannot be negative.');
                        e.preventDefault();
                        return false;
                    }

                    if (parseInt(stock) < 0) {
                        alert('Stock cannot be negative.');
                        e.preventDefault();
                        return false;
                    }
                });
            }

            console.log('Simple modal system ready - All functions should be working now!');
        });
    </script>
</body>
</html>
